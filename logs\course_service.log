2025-05-12 16:59:04,178 - course_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/courses/courses
2025-05-12 17:29:54,692 - course_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/courses/courses
2025-05-12 17:31:12,834 - course_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/courses/courses
2025-05-12 17:36:22,445 - course_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/courses/courses
2025-05-13 16:10:46,456 - course_service.common.utils - ERROR - Error: You don't have permission to create a course, Status: 403
2025-05-13 16:12:48,654 - course_service.common.utils - ERROR - Error: You don't have permission to create a course, Status: 403
2025-05-13 16:13:45,781 - course_service.common.utils - ERROR - Error: You don't have permission to create a course, Status: 403
2025-05-13 16:15:04,683 - course_service.common.utils - ERROR - Error: You don't have permission to create a course, Status: 403
2025-05-13 16:15:31,450 - course_service.common.utils - ERROR - Error: You don't have permission to create a course, Status: 403
2025-05-13 16:24:12,268 - course_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/courses/courses/1
2025-05-13 16:25:41,548 - course_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/courses/map-student
2025-05-13 16:25:52,474 - course_service.common.utils - ERROR - Error: Student not found or service unavailable, Status: 404
2025-05-13 16:26:26,557 - course_service.common.utils - ERROR - Error: Student not found or service unavailable, Status: 404
2025-05-13 16:28:15,385 - course_service.common.utils - ERROR - Error: Student not found or service unavailable, Status: 404
2025-05-13 16:40:22,932 - course_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/courses/student-courses/1
2025-05-13 18:45:18,616 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-13 18:46:57,952 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-13 18:47:01,635 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-13 18:47:11,393 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-13 18:47:20,495 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-13 19:05:36,191 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-13 19:08:48,153 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-13 19:09:02,662 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-13 19:09:22,952 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-15 11:47:18,149 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-15 11:48:12,777 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-15 11:48:17,045 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-15 12:53:54,132 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-27 11:37:45,759 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-27 11:37:46,067 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-27 15:17:39,830 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-27 15:17:40,593 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-27 15:17:40,652 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-27 15:17:48,070 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-27 16:47:44,614 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-27 16:47:44,936 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-27 16:47:47,500 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
2025-05-27 16:47:47,695 - course_service.common.middleware - WARNING - User with role Teacher attempted to access /api/courses/courses which requires roles ['Super Admin', 'Admin']
